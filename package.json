{"name": "rebang", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "devDependencies": {"@types/node": "^22.13.9", "autoprefixer": "^10.4.16", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "~5.7.2", "vite": "^6.1.0"}, "dependencies": {"@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "eslint": "^9.22.0", "query-string": "^9.1.1", "vite-plugin-pwa": "^0.21.1", "workbox-window": "^7.3.0"}}